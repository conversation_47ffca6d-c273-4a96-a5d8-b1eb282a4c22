# ==============================================================================
# GymBro Project .gitignore v2.0
#
# This file is structured to be clear, maintainable, and comprehensive.
# It covers build artifacts, IDE configurations, OS-specific files,
# local configurations, dependency caches, and project-specific temporary files.
# ==============================================================================


# ==============================================================================
# SECTION 1: Build & Compilation Artifacts
#
# These files are generated by the build process (Gradle, Java, etc.).
# They should never be committed.
# ==============================================================================

# Gradle
.gradle/
build/
**/build/
gradle-*.jar
gradle-wrapper.jar
!gradle-wrapper.properties
!gradle/wrapper/gradle-wrapper.jar

# Java / Kotlin
*.class
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
*.hprof
java_pid*.hprof
hs_err_pid*

# Kotlin incremental compilation sessions
.kotlin/
**/sessions/

# Android Studio / IntelliJ native build cache
.cxx/
.externalNativeBuild/


# ==============================================================================
# SECTION 2: IDE & Editor Configuration
#
# These files are specific to a user's local development environment.
# ==============================================================================

# IntelliJ / Android Studio
.idea/
*.iml
*.ipr
*.iws
modules.xml
workspace.xml
tasks.xml

# VSCode
.vscode/

# Generic Editor Files
*.swp
*.swo
*~
*.bak
*.log
*.logs


# ==============================================================================
# SECTION 3: Local & Environment Specific Files
#
# Contains local properties, environment variables, and private keys.
# ==============================================================================
local.properties
.env
.env.*
!.env.example

# Google Services (should be added manually by each developer)
app/google-services.json


# ==============================================================================
# SECTION 4: Operating System & Dependency Cache Files
# ==============================================================================

# macOS
.DS_Store
Thumbs.db
desktop.ini

# Node.js
node_modules/
package-lock.json
yarn.lock
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.pyc
*$py.class
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
*.egg-info/

# Ruby
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/


# ==============================================================================
# SECTION 5: Project-Specific & AI-Generated Files
#
# Ignores files and directories specific to the GymBro workflow,
# AI tools (like Cursor, Claude), backups, and temporary assets.
# ==============================================================================

# AI Agent & Task Management Tools (Cursor, Claude, Task Master, etc.)
.cursor/
.cursorignore
.cursorindexingignore
.cursorrules
.firebaserc
.roo/
.rooignore
.roomodes
.windsurf/
.claude/
!.claude/specs/  # Allow committing specification files if needed

# AI-generated temporary or backup files
.cursor copy/
memory-bank
memory-bank copy
optimization-journey/
tasks.json
.taskmasterconfig
todolist.md
temp.txt
temp_*.txt
*.py # Ignore top-level python scripts, often used for one-off tasks

# Project-specific temporary directories and files
temp_*/
backups/
/backup/
captures/
pids/
*.pid
*.seed
*.pid.lock

# Specific documentation directories to ignore (e.g., drafts, archives)
/docs/Archive/
/docs/09_Archive/
/docs/repomix/
/docs copy/
/docs/edit.bak
/data/docs/施工文档.md
/designSystem/docs/

# Ignore feature-specific documentation that represents tasks, not permanent docs
/features/*/docs/*.md

# Allow committing permanent documentation
!/features/*/README.md
!/features/*/INTERFACES.md

# Ignore specific temporary directories
temp_baidu_map/
temp_baidu_sdk/
temp_backup/
temp_class/
xnotes/
mylibrary/
act/

# Ignore font assets if they are large and managed separately
/app/src/main/assets/fonts/


# ==============================================================================
# SECTION 6: Database Schemas & Logs
#
# Room database schema JSON files. Generally, these should be versioned.
# However, if your project policy is to ignore them, uncomment the line below.
# ==============================================================================

# If you do not want to commit Room schemas, uncomment the following line:
# app/schemas/

# Log files generated during development
logs/
dev-debug.log
app/results.txt

# mcp
serena
.serena

# local docs
ai_docs
ai_review
act
.kiro
.firebase
indexdir
issues
memory-bank VS
pmd_simulator
PRPs
test-reports
tools

