package com.example.gymbro.data.workout.json

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.port.JsonProcessorPort
import com.example.gymbro.domain.workout.port.JsonDataType
import com.example.gymbro.shared.models.workout.WorkoutSessionDto
import com.example.gymbro.shared.models.workout.PlanCalendarData
import kotlinx.serialization.json.Json
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 统一JSON处理器Data层实现 v2.0 - 简化版本
 *
 * 🎯 核心架构：统一所有JSON数据类型的处理入口
 * - 实现JsonProcessorPort接口，提供Clean Architecture合规的JSON处理
 * - 使用内部JSON处理逻辑，不依赖features层
 * - 提供统一的错误处理和结果封装
 * - 支持所有数据类型的基本序列化和反序列化
 *
 * <AUTHOR> AI Assistant
 * @since 2.0.0 (简化架构版本)
 */
@Singleton
class UnifiedJsonProcessorImpl @Inject constructor() : JsonProcessorPort {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        prettyPrint = false
    }

    // ==================== Template JSON 处理方法 ====================

    override suspend fun serializeTemplate(template: WorkoutTemplate): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Template序列化开始: ${template.id}")

                // 简单的JSON序列化实现
                val jsonString = json.encodeToString(template)

                Timber.d("🔥 [UNIFIED-JSON] Template序列化成功: ${jsonString.length}字符")
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Template序列化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_SERIALIZE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Template序列化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun deserializeTemplate(jsonString: String): ModernResult<WorkoutTemplate> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Template反序列化开始: ${jsonString.length}字符")

                val template = json.decodeFromString<WorkoutTemplate>(jsonString)

                Timber.d("🔥 [UNIFIED-JSON] Template反序列化成功: ${template.id}")
                ModernResult.Success(template)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Template反序列化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_DESERIALIZE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Template反序列化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    // ==================== Session JSON 处理方法 ====================

    override suspend fun serializeSession(session: WorkoutSessionDto): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Session序列化开始: ${session.id}")

                val jsonString = json.encodeToString(session)

                Timber.d("🔥 [UNIFIED-JSON] Session序列化成功: ${jsonString.length}字符")
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Session序列化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_SERIALIZE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Session序列化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun deserializeSession(jsonString: String): ModernResult<WorkoutSessionDto> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Session反序列化开始: ${jsonString.length}字符")

                val session = json.decodeFromString<WorkoutSessionDto>(jsonString)

                Timber.d("🔥 [UNIFIED-JSON] Session反序列化成功: ${session.id}")
                ModernResult.Success(session)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Session反序列化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_DESERIALIZE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Session反序列化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun batchUpdateSession(jsonString: String, updates: List<Any>): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Session批量更新: ${updates.size}个更新")

                // 简单实现：直接返回原始JSON
                // 实际应该根据updates进行修改
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Session批量更新异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_UPDATE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Session批量更新失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    // ==================== Plan JSON 处理方法 ====================

    override suspend fun serializePlan(plan: WorkoutPlan): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Plan序列化开始: ${plan.id}")

                val jsonString = json.encodeToString(plan)

                Timber.d("🔥 [UNIFIED-JSON] Plan序列化成功: ${jsonString.length}字符")
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Plan序列化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_SERIALIZE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Plan序列化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun deserializePlan(jsonString: String): ModernResult<WorkoutPlan> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Plan反序列化开始: ${jsonString.length}字符")

                val plan = json.decodeFromString<WorkoutPlan>(jsonString)

                Timber.d("🔥 [UNIFIED-JSON] Plan反序列化成功: ${plan.id}")
                ModernResult.Success(plan)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Plan反序列化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_DESERIALIZE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Plan反序列化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun generatePlanCalendar(plan: WorkoutPlan, startDate: String): ModernResult<PlanCalendarData> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Plan日历生成开始: ${plan.id}, 开始日期: $startDate")

                // 简单实现：创建基本的日历数据
                val calendarData = PlanCalendarData(
                    planInfo = com.example.gymbro.shared.models.workout.PlanCalendarInfo(
                        planId = plan.id,
                        planName = plan.name.toString(),
                        description = plan.description?.toString(),
                        totalDays = plan.totalDays,
                        workoutDays = plan.getTotalWorkoutDays(),
                        restDays = plan.getTotalRestDays(),
                        createdAt = plan.createdAt,
                        updatedAt = plan.updatedAt
                    ),
                    calendarEntries = emptyList()
                )

                Timber.d("🔥 [UNIFIED-JSON] Plan日历生成成功")
                ModernResult.Success(calendarData)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Plan日历生成异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_CALENDAR_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Plan日历生成失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun batchUpdatePlan(jsonString: String, updates: List<Any>): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] Plan批量更新: ${updates.size}个更新")

                // 简单实现：直接返回原始JSON
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] Plan批量更新异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_UPDATE_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("Plan批量更新失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    // ==================== 通用 JSON 处理方法 ====================

    override suspend fun validateJson(jsonString: String): ModernResult<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] JSON验证开始: ${jsonString.length}字符")

                // 简单的JSON格式验证
                json.parseToJsonElement(jsonString)
                val isValid = true

                Timber.d("🔥 [UNIFIED-JSON] JSON验证结果: $isValid")
                ModernResult.Success(isValid)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] JSON验证异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_VALIDATION_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("JSON验证失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun formatJson(jsonString: String): ModernResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] JSON格式化开始: ${jsonString.length}字符")

                val jsonElement = json.parseToJsonElement(jsonString)
                val prettyJson = Json { prettyPrint = true }.encodeToString(jsonElement)

                Timber.d("🔥 [UNIFIED-JSON] JSON格式化成功: ${prettyJson.length}字符")
                ModernResult.Success(prettyJson)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] JSON格式化异常")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_FORMAT_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("JSON格式化失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }

    override suspend fun validateJsonByType(jsonString: String, dataType: JsonDataType): ModernResult<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("🔥 [UNIFIED-JSON] 类型验证开始: $dataType, ${jsonString.length}字符")

                val isValid = when (dataType) {
                    JsonDataType.TEMPLATE -> {
                        json.decodeFromString<WorkoutTemplate>(jsonString)
                        true
                    }
                    JsonDataType.SESSION -> {
                        json.decodeFromString<WorkoutSessionDto>(jsonString)
                        true
                    }
                    JsonDataType.PLAN -> {
                        json.decodeFromString<WorkoutPlan>(jsonString)
                        true
                    }
                    else -> false
                }

                Timber.d("🔥 [UNIFIED-JSON] 类型验证结果: $dataType = $isValid")
                ModernResult.Success(isValid)
            } catch (e: Exception) {
                Timber.e(e, "🔥 [UNIFIED-JSON] 类型验证异常: $dataType")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "JSON_TYPE_VALIDATION_ERROR",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("$dataType 类型验证失败: ${e.message}"),
                        cause = e
                    )
                )
            }
        }
    }
}
