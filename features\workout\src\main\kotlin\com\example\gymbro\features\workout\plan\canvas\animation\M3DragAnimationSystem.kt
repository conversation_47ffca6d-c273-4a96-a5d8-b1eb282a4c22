package com.example.gymbro.features.workout.plan.canvas.animation

import androidx.compose.animation.core.*
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.unit.dp
import kotlin.math.abs

/**
 * M3标准拖拽动画系统
 * 
 * 🎯 设计目标：
 * - 遵循Material Design 3动画规范
 * - 提供流畅的拖拽视觉反馈
 * - 智能的触觉反馈集成
 * - 性能优化的动画实现
 * 
 * 🏗️ 核心组件：
 * - DragAnimationSpec: 拖拽动画规格定义
 * - M3DragModifier: 统一的拖拽动画修饰符
 * - HapticFeedbackController: 触觉反馈控制器
 * - AnimationStateManager: 动画状态管理器
 */

/**
 * M3拖拽动画规格
 * 
 * 基于Material Design 3规范定义的动画参数
 */
object M3DragAnimationSpec {
    
    /**
     * 拖拽开始动画
     * - 缩放：1.0 -> 1.05
     * - 透明度：1.0 -> 0.9  
     * - 阴影：2dp -> 8dp
     * - 持续时间：200ms
     * - 缓动函数：FastOutSlowInEasing
     */
    val dragStartAnimation = tween<Float>(
        durationMillis = 200,
        easing = FastOutSlowInEasing
    )
    
    /**
     * 拖拽结束动画
     * - 缩放：1.05 -> 1.0
     * - 透明度：0.9 -> 1.0
     * - 阴影：8dp -> 2dp  
     * - 持续时间：300ms
     * - 缓动函数：FastOutSlowInEasing
     */
    val dragEndAnimation = tween<Float>(
        durationMillis = 300,
        easing = FastOutSlowInEasing
    )
    
    /**
     * Drop Zone高亮动画
     * - 边框透明度：0.3 -> 1.0
     * - 背景透明度：0.0 -> 0.1
     * - 缩放：1.0 -> 1.02
     * - 持续时间：250ms
     * - 缓动函数：SpringSpec with medium bouncy
     */
    val dropZoneHighlightAnimation = spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessMedium
    )
    
    /**
     * 拖拽跟随动画
     * - 位置跟随：即时响应，无延迟
     * - 旋转：±2度微妙摆动
     * - 持续时间：100ms
     * - 缓动函数：LinearOutSlowInEasing  
     */
    val dragFollowAnimation = tween<Float>(
        durationMillis = 100,
        easing = LinearOutSlowInEasing
    )
    
    /**
     * 放置成功动画
     * - 缩放：1.05 -> 0.95 -> 1.0 (弹性效果)
     * - 透明度：0.9 -> 1.0
     * - 持续时间：400ms
     * - 缓动函数：弹性动画
     */
    val dropSuccessAnimation = keyframes<Float> {
        durationMillis = 400
        0.95f at 100 using FastOutSlowInEasing
        1.02f at 200 using FastOutSlowInEasing  
        1.0f at 400 using FastOutSlowInEasing
    }
    
    /**
     * 拖拽取消动画
     * - 位置：当前位置 -> 原始位置
     * - 缩放：1.05 -> 1.0
     * - 透明度：0.9 -> 1.0
     * - 持续时间：250ms
     * - 缓动函数：FastOutSlowInEasing
     */
    val dragCancelAnimation = tween<Float>(
        durationMillis = 250,
        easing = FastOutSlowInEasing
    )
}

/**
 * M3触觉反馈控制器
 */
class M3HapticFeedbackController {
    
    /**
     * 拖拽开始反馈
     * - 类型：LongPress
     * - 强度：中等
     */
    fun onDragStart(hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback) {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
    }
    
    /**
     * 进入Drop Zone反馈
     * - 类型：TextHandleMove  
     * - 强度：轻微
     */
    fun onEnterDropZone(hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback) {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
    }
    
    /**
     * 放置成功反馈
     * - 类型：LongPress
     * - 强度：中等
     */
    fun onDropSuccess(hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback) {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
    }
    
    /**
     * 拖拽取消反馈
     * - 类型：TextHandleMove
     * - 强度：轻微
     */
    fun onDragCancel(hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback) {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
    }
}

/**
 * 拖拽动画状态
 */
data class DragAnimationState(
    val isDragging: Boolean = false,
    val isOverDropZone: Boolean = false,
    val dragOffset: Offset = Offset.Zero,
    val scale: Float = 1f,
    val alpha: Float = 1f,
    val elevation: Float = 2f,
    val rotation: Float = 0f
)

/**
 * M3拖拽动画修饰符
 * 
 * 提供完整的M3标准拖拽动画支持
 */
@Composable
fun Modifier.m3DragAnimation(
    isDragging: Boolean,
    isOverDropZone: Boolean = false,
    dragOffset: Offset = Offset.Zero,
    onDragStart: () -> Unit = {},
    onDragEnd: () -> Unit = {},
    enabled: Boolean = true
): Modifier = composed {
    
    val hapticFeedback = LocalHapticFeedback.current
    val hapticController = remember { M3HapticFeedbackController() }
    
    // 动画状态
    val scale by animateFloatAsState(
        targetValue = when {
            isDragging -> 1.05f
            isOverDropZone -> 1.02f
            else -> 1f
        },
        animationSpec = if (isDragging) {
            M3DragAnimationSpec.dragStartAnimation
        } else {
            M3DragAnimationSpec.dragEndAnimation
        },
        label = "drag_scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isDragging) 0.9f else 1f,
        animationSpec = if (isDragging) {
            M3DragAnimationSpec.dragStartAnimation
        } else {
            M3DragAnimationSpec.dragEndAnimation
        },
        label = "drag_alpha"
    )
    
    val elevation by animateFloatAsState(
        targetValue = when {
            isDragging -> 8f
            isOverDropZone -> 4f
            else -> 2f
        },
        animationSpec = M3DragAnimationSpec.dragStartAnimation,
        label = "drag_elevation"
    )
    
    val rotation by animateFloatAsState(
        targetValue = if (isDragging) {
            // 基于拖拽速度计算微妙的旋转角度
            (dragOffset.x * 0.1f).coerceIn(-2f, 2f)
        } else {
            0f
        },
        animationSpec = M3DragAnimationSpec.dragFollowAnimation,
        label = "drag_rotation"
    )
    
    // 触觉反馈处理
    LaunchedEffect(isDragging) {
        if (isDragging) {
            hapticController.onDragStart(hapticFeedback)
            onDragStart()
        } else {
            onDragEnd()
        }
    }
    
    LaunchedEffect(isOverDropZone) {
        if (isOverDropZone && isDragging) {
            hapticController.onEnterDropZone(hapticFeedback)
        }
    }
    
    this
        .scale(scale)
        .graphicsLayer {
            this.alpha = alpha
            rotationZ = rotation
        }
        .shadow(elevation = elevation.dp)
}

/**
 * Drop Zone动画修饰符
 * 
 * 为Drop Zone提供M3标准的高亮动画
 */
@Composable
fun Modifier.m3DropZoneAnimation(
    isHighlighted: Boolean,
    canAcceptDrop: Boolean = true
): Modifier = composed {
    
    val borderAlpha by animateFloatAsState(
        targetValue = if (isHighlighted && canAcceptDrop) 1f else 0.3f,
        animationSpec = M3DragAnimationSpec.dropZoneHighlightAnimation,
        label = "border_alpha"
    )
    
    val backgroundAlpha by animateFloatAsState(
        targetValue = if (isHighlighted && canAcceptDrop) 0.1f else 0f,
        animationSpec = M3DragAnimationSpec.dropZoneHighlightAnimation,  
        label = "background_alpha"
    )
    
    val scale by animateFloatAsState(
        targetValue = if (isHighlighted && canAcceptDrop) 1.02f else 1f,
        animationSpec = M3DragAnimationSpec.dropZoneHighlightAnimation,
        label = "dropzone_scale"
    )
    
    this
        .scale(scale)
        .graphicsLayer {
            // 这里可以设置背景色透明度等效果
            // 具体实现取决于组件的背景绘制方式
        }
}

/**
 * 放置成功动画修饰符
 * 
 * 当项目成功放置时的庆祝动画
 */
@Composable  
fun Modifier.m3DropSuccessAnimation(
    isTriggered: Boolean,
    onAnimationEnd: () -> Unit = {}
): Modifier = composed {
    
    var animationTriggered by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (animationTriggered) 1f else 1f, 
        animationSpec = M3DragAnimationSpec.dropSuccessAnimation,
        finishedListener = { onAnimationEnd() },
        label = "drop_success_scale"
    )
    
    // 触发动画
    LaunchedEffect(isTriggered) {
        if (isTriggered && !animationTriggered) {
            animationTriggered = true
        }
    }
    
    // 重置动画状态
    LaunchedEffect(animationTriggered) {
        if (animationTriggered) {
            // 动画完成后重置状态
            kotlinx.coroutines.delay(500)
            animationTriggered = false
        }
    }
    
    this.scale(scale)
}

/**
 * 拖拽路径追踪动画
 * 
 * 为拖拽项目提供平滑的路径跟随动画
 */
@Composable
fun Modifier.m3DragPathAnimation(
    targetOffset: Offset,
    isAnimating: Boolean = true
): Modifier = composed {
    
    val animatedOffset by animateOffsetAsState(
        targetValue = targetOffset,
        animationSpec = if (isAnimating) {
            M3DragAnimationSpec.dragFollowAnimation
        } else {
            snap() // 立即跳转，无动画
        },
        label = "drag_path"
    )
    
    this.graphicsLayer {
        translationX = animatedOffset.x
        translationY = animatedOffset.y
    }
}

/**
 * 智能拖拽手势检测修饰符
 * 
 * 集成M3动画的智能拖拽手势检测
 */
@Composable
fun Modifier.m3SmartDragGestures(
    onDragStart: (Offset) -> Unit,
    onDragEnd: () -> Unit,
    onDrag: (Offset) -> Unit,
    enabled: Boolean = true,
    dragThreshold: Float = 10f // 拖拽启动阈值
): Modifier = composed {
    
    val hapticFeedback = LocalHapticFeedback.current
    val hapticController = remember { M3HapticFeedbackController() }
    
    var isDragging by remember { mutableStateOf(false) }
    var startOffset by remember { mutableStateOf(Offset.Zero) }
    
    this.pointerInput(enabled) {
        if (!enabled) return@pointerInput
        
        detectDragGestures(
            onDragStart = { offset ->
                startOffset = offset
                isDragging = false // 等待超过阈值再开始拖拽
            },
            onDragEnd = {
                if (isDragging) {
                    isDragging = false
                    hapticController.onDragCancel(hapticFeedback)
                    onDragEnd()
                }
            },
            onDrag = { change ->
                val currentOffset = startOffset + change
                val distance = abs((currentOffset - startOffset).getDistance())
                
                if (!isDragging && distance > dragThreshold) {
                    // 超过阈值，开始拖拽
                    isDragging = true
                    hapticController.onDragStart(hapticFeedback)
                    onDragStart(startOffset)
                }
                
                if (isDragging) {
                    onDrag(change)
                }
            }
        )
    }
}

// === 扩展函数 ===

/**
 * Offset距离计算扩展
 */
private fun Offset.getDistance(): Float {
    return kotlin.math.sqrt(x * x + y * y)
}

/**
 * 动画偏移状态
 */
@Composable
private fun animateOffsetAsState(
    targetValue: Offset,
    animationSpec: AnimationSpec<Offset> = spring(),
    label: String = "OffsetAnimation"
): State<Offset> {
    return animateValueAsState(
        targetValue = targetValue,
        typeConverter = Offset.VectorConverter,
        animationSpec = animationSpec,
        label = label
    )
}