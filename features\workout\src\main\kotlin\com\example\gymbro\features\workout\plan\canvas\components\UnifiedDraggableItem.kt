package com.example.gymbro.features.workout.plan.canvas.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.plan.canvas.coordinator.UnifiedDragCoordinator
import com.example.gymbro.features.workout.plan.canvas.coordinator.draggable

/**
 * 统一可拖拽项组件 - 支持Template、TemplateDraft和Plan Item
 * 
 * 🎯 核心特性：
 * - 统一的拖拽界面：Template和TemplateDraft使用相同的UI模式
 * - M3设计规范：完整的Material Design 3视觉效果
 * - 类型安全：使用sealed class确保数据类型安全
 * - 智能动画：拖拽状态的流畅视觉反馈
 * - 性能优化：最小重组，智能记忆化
 * 
 * 🏗️ 设计原则：
 * - 复用性：单一组件支持多种数据类型
 * - 一致性：统一的视觉语言和交互模式
 * - 可扩展性：易于添加新的拖拽项类型
 */

/**
 * 可拖拽项数据密封类
 */
sealed class DraggableItemData {
    abstract val id: String
    abstract val displayName: String
    abstract val summary: String
    abstract val estimatedDuration: Int?
    abstract val exerciseCount: Int
    abstract val targetMuscleGroups: List<String>
    abstract val tags: List<String>
    
    /**
     * Template数据
     */
    data class TemplateData(
        override val id: String,
        override val displayName: String,
        override val summary: String,
        override val estimatedDuration: Int?,
        override val exerciseCount: Int,
        override val targetMuscleGroups: List<String>,
        override val tags: List<String>,
        val template: WorkoutTemplate
    ) : DraggableItemData() {
        companion object {
            fun fromTemplate(template: WorkoutTemplate): TemplateData {
                return TemplateData(
                    id = template.id,
                    displayName = template.name.toString(),
                    summary = "${template.exercises.size} 个动作",
                    estimatedDuration = template.estimatedDuration,
                    exerciseCount = template.exercises.size,
                    targetMuscleGroups = template.targetMuscleGroups ?: emptyList(),
                    tags = template.tags ?: emptyList(),
                    template = template
                )
            }
        }
    }
    
    /**
     * TemplateDraft数据
     */
    data class DraftData(
        override val id: String,
        override val displayName: String,
        override val summary: String,
        override val estimatedDuration: Int?,
        override val exerciseCount: Int,
        override val targetMuscleGroups: List<String>,
        override val tags: List<String>,
        val draft: TemplateDraft,
        val canPromote: Boolean,
        val version: Int
    ) : DraggableItemData() {
        companion object {
            fun fromDraft(draft: TemplateDraft): DraftData {
                return DraftData(
                    id = draft.id,
                    displayName = draft.name,
                    summary = draft.summary,
                    estimatedDuration = draft.estimatedDuration,
                    exerciseCount = draft.exercises.size,
                    targetMuscleGroups = draft.targetMuscleGroups,
                    tags = draft.tags,
                    draft = draft,
                    canPromote = draft.canPromoteToTemplate(),
                    version = draft.version
                )
            }
        }
    }
}

/**
 * 统一可拖拽项组件
 */
@Composable
fun UnifiedDraggableItem(
    itemData: DraggableItemData,
    coordinator: UnifiedDragCoordinator,
    onLongPress: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    isEnabled: Boolean = true
) {
    val dragCoordinatorData = remember(itemData) {
        UnifiedDragCoordinator.DraggableItemData(
            id = itemData.id,
            type = when (itemData) {
                is DraggableItemData.TemplateData -> UnifiedDragCoordinator.DragItemType.TEMPLATE
                is DraggableItemData.DraftData -> UnifiedDragCoordinator.DragItemType.DRAFT
            },
            sourceData = when (itemData) {
                is DraggableItemData.TemplateData -> itemData.template
                is DraggableItemData.DraftData -> itemData.draft
            },
            displayName = itemData.displayName,
            summary = itemData.summary,
            estimatedDuration = itemData.estimatedDuration
        )
    }
    
    // 拖拽状态监听
    val dragState by coordinator.dragState.collectAsState()
    val isDragging = dragState is UnifiedDragCoordinator.DragState.Dragging && 
                    dragState.item.id == itemData.id
    
    // 动画效果
    val scale by animateFloatAsState(
        targetValue = if (isDragging) 1.05f else 1f,
        animationSpec = tween(
            durationMillis = 200,
            easing = FastOutSlowInEasing
        ),
        label = "scale_animation"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isDragging) 0.8f else 1f,
        animationSpec = tween(
            durationMillis = 200,
            easing = FastOutSlowInEasing
        ),
        label = "alpha_animation"
    )
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
            .scale(scale)
            .alpha(alpha)
            .draggable(
                coordinator = coordinator,
                item = dragCoordinatorData,
                enabled = isEnabled
            ),
        shape = RoundedCornerShape(Tokens.Radius.Card),
        color = getItemBackgroundColor(itemData, isDragging),
        shadowElevation = if (isDragging) Tokens.Elevation.Medium else Tokens.Elevation.Small,
        tonalElevation = Tokens.Elevation.Small
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Small),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // 顶部：类型图标和拖拽手柄
            ItemHeader(
                itemType = when (itemData) {
                    is DraggableItemData.TemplateData -> ItemType.TEMPLATE
                    is DraggableItemData.DraftData -> ItemType.DRAFT
                },
                isDragging = isDragging
            )
            
            // 中间：标题和摘要
            ItemContent(
                displayName = itemData.displayName,
                summary = itemData.summary,
                isDragging = isDragging
            )
            
            // 底部：统计信息和标签
            ItemFooter(
                exerciseCount = itemData.exerciseCount,
                estimatedDuration = itemData.estimatedDuration,
                tags = itemData.tags.take(2), // 最多显示2个标签
                itemData = itemData
            )
        }
    }
}

/**
 * 项目类型枚举
 */
private enum class ItemType {
    TEMPLATE,
    DRAFT
}

/**
 * 项目头部组件
 */
@Composable
private fun ItemHeader(
    itemType: ItemType,
    isDragging: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        // 类型图标
        Icon(
            imageVector = when (itemType) {
                ItemType.TEMPLATE -> Icons.Default.FitnessCenter
                ItemType.DRAFT -> Icons.Default.Edit
            },
            contentDescription = null,
            tint = when (itemType) {
                ItemType.TEMPLATE -> MaterialTheme.workoutColors.accentPrimary
                ItemType.DRAFT -> MaterialTheme.workoutColors.accentSecondary
            },
            modifier = Modifier.size(18.dp)
        )
        
        // 类型标签
        ItemTypeChip(itemType = itemType)
        
        // 拖拽手柄
        Icon(
            imageVector = Icons.Default.DragHandle,
            contentDescription = "拖拽${if (itemType == ItemType.TEMPLATE) "模板" else "草稿"}",
            tint = if (isDragging) {
                MaterialTheme.workoutColors.accentPrimary
            } else {
                MaterialTheme.workoutColors.textSecondary
            },
            modifier = Modifier.size(16.dp)
        )
    }
}

/**
 * 类型标签组件
 */
@Composable
private fun ItemTypeChip(
    itemType: ItemType,
    modifier: Modifier = Modifier
) {
    val (label, color) = when (itemType) {
        ItemType.TEMPLATE -> "模板" to MaterialTheme.workoutColors.accentPrimary
        ItemType.DRAFT -> "草稿" to MaterialTheme.workoutColors.accentSecondary
    }
    
    Box(
        modifier = modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(Tokens.Radius.Small)
            )
            .padding(
                horizontal = Tokens.Spacing.XSmall,
                vertical = 2.dp
            )
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 项目内容组件
 */
@Composable
private fun ItemContent(
    displayName: String,
    summary: String,
    isDragging: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall)
    ) {
        // 标题
        Text(
            text = displayName,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
            color = if (isDragging) {
                MaterialTheme.workoutColors.textPrimary.copy(alpha = 0.8f)
            } else {
                MaterialTheme.workoutColors.textPrimary
            },
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        
        // 摘要
        Text(
            text = summary,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.workoutColors.textSecondary,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/**
 * 项目底部信息组件
 */
@Composable
private fun ItemFooter(
    exerciseCount: Int,
    estimatedDuration: Int?,
    tags: List<String>,
    itemData: DraggableItemData,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧：统计信息
        Row(
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 动作数量
            StatisticChip(
                label = "${exerciseCount}个动作",
                color = MaterialTheme.workoutColors.textSecondary
            )
            
            // 预估时长
            estimatedDuration?.let { duration ->
                StatisticChip(
                    label = "${duration}分钟",
                    color = MaterialTheme.workoutColors.accentSecondary
                )
            }
        }
        
        // 右侧：特殊状态指示器
        ItemSpecialIndicator(itemData = itemData)
    }
}

/**
 * 统计标签组件
 */
@Composable
private fun StatisticChip(
    label: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(Tokens.Radius.Small)
            )
            .padding(
                horizontal = Tokens.Spacing.XSmall,
                vertical = 2.dp
            )
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 特殊状态指示器
 */
@Composable
private fun ItemSpecialIndicator(
    itemData: DraggableItemData,
    modifier: Modifier = Modifier
) {
    when (itemData) {
        is DraggableItemData.DraftData -> {
            if (itemData.canPromote) {
                Box(
                    modifier = modifier
                        .background(
                            color = MaterialTheme.workoutColors.success.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(Tokens.Radius.Small)
                        )
                        .padding(
                            horizontal = Tokens.Spacing.XSmall,
                            vertical = 2.dp
                        )
                ) {
                    Text(
                        text = "可转正",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.workoutColors.success,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        is DraggableItemData.TemplateData -> {
            // Template可以显示其他状态指示器
        }
    }
}

/**
 * 获取项目背景颜色
 */
@Composable
private fun getItemBackgroundColor(
    itemData: DraggableItemData,
    isDragging: Boolean
): Color {
    val baseColor = when (itemData) {
        is DraggableItemData.TemplateData -> MaterialTheme.workoutColors.cardBackground
        is DraggableItemData.DraftData -> MaterialTheme.workoutColors.cardBackground
    }
    
    return if (isDragging) {
        when (itemData) {
            is DraggableItemData.TemplateData -> 
                MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
            is DraggableItemData.DraftData -> 
                MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f)
        }
    } else {
        baseColor
    }
}

// === 预览数据 ===

/**
 * 创建预览用的Template数据
 */
fun createPreviewTemplateData(): DraggableItemData.TemplateData {
    // 这里需要创建一个mock的WorkoutTemplate
    // 实际实现中应该使用真实的数据构造方法
    return DraggableItemData.TemplateData(
        id = "template_001",
        displayName = "胸部力量训练",
        summary = "5 个动作",
        estimatedDuration = 45,
        exerciseCount = 5,
        targetMuscleGroups = listOf("胸部", "三头肌"),
        tags = listOf("力量", "上肢"),
        template = WorkoutTemplate(
            id = "template_001",
            name = com.example.gymbro.core.ui.text.UiText.DynamicString("胸部力量训练"),
            exercises = emptyList()
        )
    )
}

/**
 * 创建预览用的Draft数据
 */
fun createPreviewDraftData(): DraggableItemData.DraftData {
    // 这里需要创建一个mock的TemplateDraft
    // 实际实现中应该使用真实的数据构造方法
    return DraggableItemData.DraftData(
        id = "draft_001",
        displayName = "背部训练草稿",
        summary = "总计 800.0 kg • 3 组",
        estimatedDuration = 35,
        exerciseCount = 3,
        targetMuscleGroups = listOf("背部", "二头肌"),
        tags = listOf("力量", "背部"),
        draft = TemplateDraft(
            name = "背部训练草稿",
            source = com.example.gymbro.domain.workout.model.DraftSource.USER_CREATED,
            createdAt = kotlinx.datetime.Clock.System.now(),
            updatedAt = kotlinx.datetime.Clock.System.now(),
            userId = "user_001"
        ),
        canPromote = true,
        version = 1
    )
}