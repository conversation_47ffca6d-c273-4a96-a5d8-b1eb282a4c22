package com.example.gymbro.features.workout.plan.canvas.coordinator

import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.offset
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.zIndex
import com.example.gymbro.features.workout.plan.canvas.model.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlin.math.roundToInt

/**
 * 统一拖拽协调器 - Plan画布拖拽系统的核心控制器
 * 
 * 🎯 核心职责：
 * - 管理所有拖拽操作的统一状态
 * - 协调Template、TemplateDraft、Plan item之间的拖拽
 * - 提供事件总线机制，解耦拖拽操作和UI响应
 * - 集成M3标准动画和触觉反馈
 * - 支持撤销/重做操作
 * 
 * 🏗️ 设计原则：
 * - 单一数据源：所有拖拽状态统一管理
 * - 事件驱动：基于事件总线的松耦合架构
 * - 类型安全：使用sealed class确保类型安全
 * - 性能优化：最小化重组，智能边界检测
 */
class UnifiedDragCoordinator {
    
    // === 内部状态管理 ===
    private val _dragState = MutableStateFlow(DragState.Idle)
    val dragState: StateFlow<DragState> = _dragState.asStateFlow()
    
    private val _canvasData = MutableStateFlow<PlanCanvasData?>(null)
    val canvasData: StateFlow<PlanCanvasData?> = _canvasData.asStateFlow()
    
    private val _dropZones = MutableStateFlow<List<DropZone>>(emptyList())
    val dropZones: StateFlow<List<DropZone>> = _dropZones.asStateFlow()
    
    // === 事件监听器 ===
    private val _eventListeners = mutableListOf<DragEventListener>()
    
    /**
     * 拖拽状态密封类
     */
    sealed class DragState {
        object Idle : DragState()
        
        data class Dragging(
            val item: DraggableItemData,
            val currentOffset: Offset,
            val startOffset: Offset,
            val targetDropZone: DropZone? = null
        ) : DragState()
        
        data class Dropping(
            val item: DraggableItemData,
            val targetDropZone: DropZone,
            val animationProgress: Float = 0f
        ) : DragState()
    }
    
    /**
     * 可拖拽项数据
     */
    data class DraggableItemData(
        val id: String,
        val type: DragItemType,
        val sourceData: Any, // WorkoutTemplate 或 TemplateDraft
        val displayName: String,
        val summary: String,
        val estimatedDuration: Int?
    )
    
    /**
     * 拖拽项类型
     */
    enum class DragItemType {
        TEMPLATE,
        DRAFT,
        EXISTING_PLAN_ITEM
    }
    
    /**
     * 放置区域定义
     */
    data class DropZone(
        val id: String,
        val position: CanvasPosition,
        val bounds: androidx.compose.ui.geometry.Rect,
        val isActive: Boolean = false,
        val canAccept: (DraggableItemData) -> Boolean = { true }
    )
    
    /**
     * 拖拽事件监听器接口
     */
    interface DragEventListener {
        fun onDragStart(item: DraggableItemData) {}
        fun onDragMove(item: DraggableItemData, currentOffset: Offset) {}
        fun onDragEnd(item: DraggableItemData, targetDropZone: DropZone?) {}
        fun onDrop(item: DraggableItemData, dropZone: DropZone) {}
        fun onDragCancel(item: DraggableItemData) {}
    }
    
    // === 核心API方法 ===
    
    /**
     * 初始化画布数据
     */
    fun initializeCanvas(canvasData: PlanCanvasData) {
        _canvasData.value = canvasData
        updateDropZones()
    }
    
    /**
     * 注册拖拽事件监听器
     */
    fun addDragEventListener(listener: DragEventListener) {
        _eventListeners.add(listener)
    }
    
    /**
     * 移除拖拽事件监听器
     */
    fun removeDragEventListener(listener: DragEventListener) {
        _eventListeners.remove(listener)
    }
    
    /**
     * 开始拖拽操作
     */
    fun startDrag(item: DraggableItemData, startOffset: Offset) {
        _dragState.value = DragState.Dragging(
            item = item,
            currentOffset = startOffset,
            startOffset = startOffset
        )
        
        // 通知所有监听器
        _eventListeners.forEach { it.onDragStart(item) }
        
        // 更新drop zone状态
        updateDropZoneActivation(true)
    }
    
    /**
     * 更新拖拽位置
     */
    fun updateDragPosition(offset: Offset) {
        val currentState = _dragState.value
        if (currentState is DragState.Dragging) {
            val targetDropZone = findTargetDropZone(offset)
            
            _dragState.value = currentState.copy(
                currentOffset = offset,
                targetDropZone = targetDropZone
            )
            
            // 通知监听器
            _eventListeners.forEach { it.onDragMove(currentState.item, offset) }
            
            // 更新drop zone高亮状态
            updateDropZoneHighlight(targetDropZone)
        }
    }
    
    /**
     * 结束拖拽操作
     */
    fun endDrag() {
        val currentState = _dragState.value
        if (currentState is DragState.Dragging) {
            val targetDropZone = currentState.targetDropZone
            
            if (targetDropZone != null && targetDropZone.canAccept(currentState.item)) {
                // 执行放置操作
                performDrop(currentState.item, targetDropZone)
            } else {
                // 取消拖拽
                cancelDrag()
            }
            
            // 通知监听器
            _eventListeners.forEach { it.onDragEnd(currentState.item, targetDropZone) }
        }
        
        // 重置状态
        _dragState.value = DragState.Idle
        updateDropZoneActivation(false)
    }
    
    /**
     * 取消拖拽操作
     */
    fun cancelDrag() {
        val currentState = _dragState.value
        if (currentState is DragState.Dragging) {
            _eventListeners.forEach { it.onDragCancel(currentState.item) }
        }
        
        _dragState.value = DragState.Idle
        updateDropZoneActivation(false)
    }
    
    /**
     * 执行放置操作
     */
    private fun performDrop(item: DraggableItemData, dropZone: DropZone) {
        val currentCanvas = _canvasData.value ?: return
        
        // 创建画布项目
        val canvasItem = createCanvasItem(item, dropZone.position)
        
        // 更新画布数据
        val updatedSchedule = currentCanvas.scheduleItems.toMutableMap()
        val dayIndex = dropZone.position.getDayIndex()
        val existingItems = updatedSchedule[dayIndex]?.toMutableList() ?: mutableListOf()
        
        existingItems.add(canvasItem)
        updatedSchedule[dayIndex] = existingItems
        
        // 创建操作记录
        val operation = CanvasOperation.AddItem(
            timestamp = System.currentTimeMillis(),
            item = canvasItem,
            position = dropZone.position
        )
        
        // 更新画布数据
        val updatedCanvas = currentCanvas.copy(
            scheduleItems = updatedSchedule
        ).addOperation(operation)
        
        _canvasData.value = updatedCanvas
        
        // 通知监听器
        _eventListeners.forEach { it.onDrop(item, dropZone) }
        
        // 更新drop zones
        updateDropZones()
    }
    
    /**
     * 创建画布项目
     */
    private fun createCanvasItem(item: DraggableItemData, position: CanvasPosition): CanvasItem {
        return when (item.type) {
            DragItemType.TEMPLATE -> {
                val template = item.sourceData as com.example.gymbro.domain.workout.model.template.WorkoutTemplate
                CanvasItem.TemplateItem.fromTemplate(template, position)
            }
            DragItemType.DRAFT -> {
                val draft = item.sourceData as com.example.gymbro.domain.workout.model.TemplateDraft
                CanvasItem.DraftItem.fromDraft(draft, position)
            }
            DragItemType.EXISTING_PLAN_ITEM -> {
                // 处理现有计划项目的移动
                throw NotImplementedError("Moving existing plan items not yet implemented")
            }
        }
    }
    
    /**
     * 查找目标放置区域
     */
    private fun findTargetDropZone(offset: Offset): DropZone? {
        return _dropZones.value.find { dropZone ->
            dropZone.bounds.contains(offset) && dropZone.isActive
        }
    }
    
    /**
     * 更新drop zones
     */
    private fun updateDropZones() {
        val currentCanvas = _canvasData.value ?: return
        val config = currentCanvas.canvasConfig
        
        val zones = mutableListOf<DropZone>()
        
        // 生成所有可能的drop zones（基于画布配置）
        for (week in 1..config.weekCount) {
            for (day in 1..config.daysPerWeek) {
                val position = CanvasPosition(week = week, day = day, order = 0)
                val zoneId = "dropzone_${week}_${day}"
                
                // 这里的bounds需要在实际UI布局时更新
                val zone = DropZone(
                    id = zoneId,
                    position = position,
                    bounds = androidx.compose.ui.geometry.Rect.Zero, // 将在UI层更新
                    isActive = false
                )
                
                zones.add(zone)
            }
        }
        
        _dropZones.value = zones
    }
    
    /**
     * 更新drop zone激活状态
     */
    private fun updateDropZoneActivation(isActive: Boolean) {
        val currentZones = _dropZones.value
        val updatedZones = currentZones.map { it.copy(isActive = isActive) }
        _dropZones.value = updatedZones
    }
    
    /**
     * 更新drop zone高亮状态
     */
    private fun updateDropZoneHighlight(targetDropZone: DropZone?) {
        val currentZones = _dropZones.value
        val updatedZones = currentZones.map { zone ->
            zone.copy(isActive = zone.id == targetDropZone?.id)
        }
        _dropZones.value = updatedZones
    }
    
    /**
     * 更新drop zone边界信息（由UI层调用）
     */
    fun updateDropZoneBounds(zoneId: String, bounds: androidx.compose.ui.geometry.Rect) {
        val currentZones = _dropZones.value
        val updatedZones = currentZones.map { zone ->
            if (zone.id == zoneId) zone.copy(bounds = bounds) else zone
        }
        _dropZones.value = updatedZones
    }
    
    // === 撤销/重做功能 ===
    
    /**
     * 撤销最后一个操作
     */
    fun undo(): Boolean {
        val currentCanvas = _canvasData.value ?: return false
        
        if (!currentCanvas.canUndo()) return false
        
        val newIndex = currentCanvas.currentOperationIndex - 1
        val targetOperation = currentCanvas.operationHistory[newIndex]
        
        // 根据操作类型执行撤销
        val updatedCanvas = when (targetOperation) {
            is CanvasOperation.AddItem -> {
                // 撤销添加：移除该项目
                removeItemFromCanvas(currentCanvas, targetOperation.item.id)
            }
            is CanvasOperation.MoveItem -> {
                // 撤销移动：恢复到原位置
                moveItemInCanvas(currentCanvas, targetOperation.itemId, targetOperation.fromPosition)
            }
            is CanvasOperation.RemoveItem -> {
                // 撤销删除：重新添加项目
                addItemToCanvas(currentCanvas, targetOperation.item)
            }
            is CanvasOperation.UpdateItem -> {
                // 撤销更新：恢复原项目
                updateItemInCanvas(currentCanvas, targetOperation.oldItem)
            }
            is CanvasOperation.CreateCanvas -> {
                // 无法撤销画布创建
                currentCanvas
            }
        }
        
        _canvasData.value = updatedCanvas.copy(currentOperationIndex = newIndex)
        updateDropZones()
        return true
    }
    
    /**
     * 重做下一个操作
     */
    fun redo(): Boolean {
        val currentCanvas = _canvasData.value ?: return false
        
        if (!currentCanvas.canRedo()) return false
        
        val newIndex = currentCanvas.currentOperationIndex + 1
        val targetOperation = currentCanvas.operationHistory[newIndex]
        
        // 根据操作类型执行重做
        val updatedCanvas = when (targetOperation) {
            is CanvasOperation.AddItem -> {
                addItemToCanvas(currentCanvas, targetOperation.item)
            }
            is CanvasOperation.MoveItem -> {
                moveItemInCanvas(currentCanvas, targetOperation.itemId, targetOperation.toPosition)
            }
            is CanvasOperation.RemoveItem -> {
                removeItemFromCanvas(currentCanvas, targetOperation.item.id)
            }
            is CanvasOperation.UpdateItem -> {
                updateItemInCanvas(currentCanvas, targetOperation.newItem)
            }
            is CanvasOperation.CreateCanvas -> {
                currentCanvas
            }
        }
        
        _canvasData.value = updatedCanvas.copy(currentOperationIndex = newIndex)
        updateDropZones()
        return true
    }
    
    // === 画布操作辅助方法 ===
    
    private fun addItemToCanvas(canvas: PlanCanvasData, item: CanvasItem): PlanCanvasData {
        val updatedSchedule = canvas.scheduleItems.toMutableMap()
        val dayIndex = item.position.getDayIndex()
        val existingItems = updatedSchedule[dayIndex]?.toMutableList() ?: mutableListOf()
        existingItems.add(item)
        updatedSchedule[dayIndex] = existingItems
        return canvas.copy(scheduleItems = updatedSchedule)
    }
    
    private fun removeItemFromCanvas(canvas: PlanCanvasData, itemId: String): PlanCanvasData {
        val updatedSchedule = canvas.scheduleItems.toMutableMap()
        updatedSchedule.forEach { (dayIndex, items) ->
            val filteredItems = items.filter { it.id != itemId }
            if (filteredItems.isEmpty()) {
                updatedSchedule.remove(dayIndex)
            } else {
                updatedSchedule[dayIndex] = filteredItems
            }
        }
        return canvas.copy(scheduleItems = updatedSchedule)
    }
    
    private fun moveItemInCanvas(canvas: PlanCanvasData, itemId: String, newPosition: CanvasPosition): PlanCanvasData {
        // 先找到并移除原项目
        var itemToMove: CanvasItem? = null
        val updatedSchedule = canvas.scheduleItems.toMutableMap()
        
        updatedSchedule.forEach { (dayIndex, items) ->
            items.find { it.id == itemId }?.let { item ->
                itemToMove = item
                val filteredItems = items.filter { it.id != itemId }
                if (filteredItems.isEmpty()) {
                    updatedSchedule.remove(dayIndex)
                } else {
                    updatedSchedule[dayIndex] = filteredItems
                }
                return@forEach
            }
        }
        
        // 将项目添加到新位置
        itemToMove?.let { item ->
            val newDayIndex = newPosition.getDayIndex()
            val existingItems = updatedSchedule[newDayIndex]?.toMutableList() ?: mutableListOf()
            val updatedItem = when (item) {
                is CanvasItem.TemplateItem -> item.copy(position = newPosition)
                is CanvasItem.DraftItem -> item.copy(position = newPosition)
                is CanvasItem.CustomItem -> item.copy(position = newPosition)
            }
            existingItems.add(updatedItem)
            updatedSchedule[newDayIndex] = existingItems
        }
        
        return canvas.copy(scheduleItems = updatedSchedule)
    }
    
    private fun updateItemInCanvas(canvas: PlanCanvasData, updatedItem: CanvasItem): PlanCanvasData {
        val updatedSchedule = canvas.scheduleItems.toMutableMap()
        val dayIndex = updatedItem.position.getDayIndex()
        val existingItems = updatedSchedule[dayIndex]?.toMutableList() ?: mutableListOf()
        
        val itemIndex = existingItems.indexOfFirst { it.id == updatedItem.id }
        if (itemIndex >= 0) {
            existingItems[itemIndex] = updatedItem
            updatedSchedule[dayIndex] = existingItems
        }
        
        return canvas.copy(scheduleItems = updatedSchedule)
    }
}

/**
 * 拖拽修饰符扩展 - 为Compose组件提供统一的拖拽能力
 */
@Composable
fun Modifier.draggable(
    coordinator: UnifiedDragCoordinator,
    item: UnifiedDragCoordinator.DraggableItemData,
    enabled: Boolean = true
): Modifier = composed {
    val hapticFeedback = LocalHapticFeedback.current
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var isDragging by remember { mutableStateOf(false) }
    
    val dragState by coordinator.dragState.collectAsState()
    val isCurrentlyDragging = dragState is UnifiedDragCoordinator.DragState.Dragging &&
            dragState.item.id == item.id
    
    if (isCurrentlyDragging && dragState is UnifiedDragCoordinator.DragState.Dragging) {
        dragOffset = dragState.currentOffset - dragState.startOffset
    }
    
    this
        .offset { IntOffset(dragOffset.x.roundToInt(), dragOffset.y.roundToInt()) }
        .graphicsLayer {
            scaleX = if (isCurrentlyDragging) 1.05f else 1f
            scaleY = if (isCurrentlyDragging) 1.05f else 1f
            alpha = if (isCurrentlyDragging) 0.8f else 1f
        }
        .zIndex(if (isCurrentlyDragging) 1f else 0f)
        .pointerInput(item.id, enabled) {
            if (!enabled) return@pointerInput
            
            detectDragGestures(
                onDragStart = { offset ->
                    isDragging = true
                    dragOffset = Offset.Zero
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    coordinator.startDrag(item, offset)
                },
                onDragEnd = {
                    isDragging = false
                    dragOffset = Offset.Zero
                    coordinator.endDrag()
                },
                onDrag = { change ->
                    dragOffset += change
                    coordinator.updateDragPosition(dragOffset)
                }
            )
        }
}