package com.example.gymbro.features.workout.plan.canvas.sync

import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.plan.canvas.model.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 跨模块数据同步管理器
 * 
 * 🎯 核心职责：
 * - Template模块和Plan模块的数据同步
 * - TemplateDraft变更监听和Plan引用更新  
 * - 数据一致性保证和冲突解决
 * - 事务性更新和回滚机制
 * 
 * 🏗️ 架构特点：
 * - 事件驱动：基于Flow的响应式数据同步
 * - 双向同步：Plan ↔ Template的双向数据绑定
 * - 冲突解决：智能的数据冲突检测和解决
 * - 性能优化：增量更新和批量同步
 */
@Singleton
class CrossModuleDataSyncManager @Inject constructor(
    private val logger: Logger
) {
    
    // === 内部状态管理 ===
    
    private val _syncEvents = MutableSharedFlow<SyncEvent>(replay = 0)
    val syncEvents: SharedFlow<SyncEvent> = _syncEvents.asSharedFlow()
    
    private val _syncState = MutableStateFlow(SyncState())
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
    
    // 数据缓存
    private val templateCache = mutableMapOf<String, WorkoutTemplate>()
    private val draftCache = mutableMapOf<String, TemplateDraft>()
    private val planCache = mutableMapOf<String, PlanCanvasData>()
    
    // 同步监听器
    private val syncListeners = mutableListOf<SyncEventListener>()
    
    /**
     * 同步事件类型
     */
    sealed class SyncEvent {
        abstract val timestamp: Long
        abstract val sourceModule: String
        
        data class TemplateUpdated(
            override val timestamp: Long,
            override val sourceModule: String,
            val template: WorkoutTemplate,
            val changeType: ChangeType
        ) : SyncEvent()
        
        data class DraftUpdated(
            override val timestamp: Long,  
            override val sourceModule: String,
            val draft: TemplateDraft,
            val changeType: ChangeType
        ) : SyncEvent()
        
        data class PlanUpdated(
            override val timestamp: Long,
            override val sourceModule: String,
            val planCanvas: PlanCanvasData,
            val changeType: ChangeType
        ) : SyncEvent()
        
        data class SyncConflict(
            override val timestamp: Long,
            override val sourceModule: String,
            val conflictType: ConflictType,
            val details: Map<String, Any>
        ) : SyncEvent()
        
        data class SyncCompleted(
            override val timestamp: Long,
            override val sourceModule: String,
            val syncedItemsCount: Int,
            val duration: Long
        ) : SyncEvent()
    }
    
    /**
     * 变更类型
     */
    enum class ChangeType {
        CREATED, UPDATED, DELETED, PROMOTED // PROMOTED for draft -> template
    }
    
    /**
     * 冲突类型
     */
    enum class ConflictType {
        VERSION_MISMATCH,
        CONCURRENT_MODIFICATION,
        REFERENCE_BROKEN,
        DATA_CORRUPTION
    }
    
    /**
     * 同步状态
     */
    data class SyncState(
        val isSyncing: Boolean = false,
        val lastSyncTime: Long = 0,
        val pendingSyncCount: Int = 0,
        val failedSyncCount: Int = 0,
        val conflictCount: Int = 0
    )
    
    /**
     * 同步事件监听器接口
     */
    interface SyncEventListener {
        fun onSyncEvent(event: SyncEvent) {}
        fun onSyncStateChanged(state: SyncState) {}
    }
    
    // === 核心同步API ===
    
    /**
     * 注册同步监听器
     */
    fun addSyncListener(listener: SyncEventListener) {
        syncListeners.add(listener)
    }
    
    /**
     * 移除同步监听器
     */
    fun removeSyncListener(listener: SyncEventListener) {
        syncListeners.remove(listener)
    }
    
    /**
     * 同步Template变更到Plan
     */
    suspend fun syncTemplateChange(
        template: WorkoutTemplate,
        changeType: ChangeType,
        scope: CoroutineScope
    ) {
        logger.d("CrossModuleSync", "同步Template变更: ${template.id}, 类型: $changeType")
        
        try {
            updateSyncState { it.copy(isSyncing = true) }
            
            // 更新缓存
            when (changeType) {
                ChangeType.CREATED, ChangeType.UPDATED -> {
                    templateCache[template.id] = template
                }
                ChangeType.DELETED -> {
                    templateCache.remove(template.id)
                }
                ChangeType.PROMOTED -> {
                    // Template promotion不适用于此场景
                }
            }
            
            // 找到所有引用此Template的Plan
            val affectedPlans = findPlansReferencingTemplate(template.id)
            
            affectedPlans.forEach { planCanvas ->
                scope.launch {
                    updatePlanTemplateReference(planCanvas, template, changeType)
                }
            }
            
            // 发送同步事件
            emitSyncEvent(
                SyncEvent.TemplateUpdated(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "template",
                    template = template,
                    changeType = changeType
                )
            )
            
        } catch (e: Exception) {
            logger.e("CrossModuleSync", "Template同步失败", e)
            handleSyncError(e, "template", template.id)
        } finally {
            updateSyncState { it.copy(isSyncing = false) }
        }
    }
    
    /**
     * 同步TemplateDraft变更到Plan
     */
    suspend fun syncDraftChange(
        draft: TemplateDraft,
        changeType: ChangeType,
        scope: CoroutineScope
    ) {
        logger.d("CrossModuleSync", "同步TemplateDraft变更: ${draft.id}, 类型: $changeType")
        
        try {
            updateSyncState { it.copy(isSyncing = true) }
            
            // 更新缓存
            when (changeType) {
                ChangeType.CREATED, ChangeType.UPDATED -> {
                    draftCache[draft.id] = draft
                }
                ChangeType.DELETED -> {
                    draftCache.remove(draft.id)
                    // 删除草稿时，需要清理Plan中的引用
                    scope.launch {
                        cleanupDraftReferencesInPlans(draft.id)
                    }
                }
                ChangeType.PROMOTED -> {
                    // 草稿转正为Template，需要更新Plan中的引用
                    scope.launch {
                        promoteDraftReferencesToTemplate(draft)
                    }
                }
            }
            
            // 找到所有引用此Draft的Plan
            val affectedPlans = findPlansReferencingDraft(draft.id)
            
            affectedPlans.forEach { planCanvas ->
                scope.launch {
                    updatePlanDraftReference(planCanvas, draft, changeType)
                }
            }
            
            // 发送同步事件
            emitSyncEvent(
                SyncEvent.DraftUpdated(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "template",
                    draft = draft,
                    changeType = changeType
                )
            )
            
        } catch (e: Exception) {
            logger.e("CrossModuleSync", "TemplateDraft同步失败", e)
            handleSyncError(e, "draft", draft.id)
        } finally {
            updateSyncState { it.copy(isSyncing = false) }
        }
    }
    
    /**
     * 同步Plan变更
     */
    suspend fun syncPlanChange(
        planCanvas: PlanCanvasData,
        changeType: ChangeType,
        scope: CoroutineScope
    ) {
        logger.d("CrossModuleSync", "同步Plan变更: ${planCanvas.canvasId}, 类型: $changeType")
        
        try {
            updateSyncState { it.copy(isSyncing = true) }
            
            // 更新缓存
            when (changeType) {
                ChangeType.CREATED, ChangeType.UPDATED -> {
                    planCache[planCanvas.canvasId] = planCanvas
                }
                ChangeType.DELETED -> {
                    planCache.remove(planCanvas.canvasId)
                }
                ChangeType.PROMOTED -> {
                    // Plan promotion不适用于此场景
                }
            }
            
            // 验证Plan中的引用完整性
            scope.launch {
                validatePlanReferences(planCanvas)
            }
            
            // 发送同步事件
            emitSyncEvent(
                SyncEvent.PlanUpdated(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "plan",
                    planCanvas = planCanvas,
                    changeType = changeType
                )
            )
            
        } catch (e: Exception) {
            logger.e("CrossModuleSync", "Plan同步失败", e)
            handleSyncError(e, "plan", planCanvas.canvasId) 
        } finally {
            updateSyncState { it.copy(isSyncing = false) }
        }
    }
    
    // === 内部同步逻辑 ===
    
    /**
     * 查找引用指定Template的所有Plan
     */
    private fun findPlansReferencingTemplate(templateId: String): List<PlanCanvasData> {
        return planCache.values.filter { planCanvas ->
            planCanvas.scheduleItems.values.flatten().any { item ->
                when (item) {
                    is CanvasItem.TemplateItem -> item.templateId == templateId
                    else -> false
                }
            }
        }
    }
    
    /**
     * 查找引用指定Draft的所有Plan
     */
    private fun findPlansReferencingDraft(draftId: String): List<PlanCanvasData> {
        return planCache.values.filter { planCanvas ->
            planCanvas.scheduleItems.values.flatten().any { item ->
                when (item) {
                    is CanvasItem.DraftItem -> item.draftId == draftId
                    else -> false
                }
            }
        }
    }
    
    /**
     * 更新Plan中的Template引用
     */
    private suspend fun updatePlanTemplateReference(
        planCanvas: PlanCanvasData,
        template: WorkoutTemplate,
        changeType: ChangeType
    ) {
        when (changeType) {
            ChangeType.UPDATED -> {
                // 更新Plan中的Template信息
                val updatedSchedule = updateTemplateItemsInSchedule(
                    planCanvas.scheduleItems,
                    template
                )
                
                val updatedPlan = planCanvas.copy(
                    scheduleItems = updatedSchedule,
                    metadata = planCanvas.metadata.copy(
                        updatedAt = Clock.System.now().toEpochMilliseconds()
                    )
                )
                
                planCache[planCanvas.canvasId] = updatedPlan
            }
            
            ChangeType.DELETED -> {
                // Template被删除，从Plan中移除相关引用或标记为损坏
                val updatedSchedule = removeTemplateItemsFromSchedule(
                    planCanvas.scheduleItems,
                    template.id
                )
                
                val updatedPlan = planCanvas.copy(
                    scheduleItems = updatedSchedule,
                    metadata = planCanvas.metadata.copy(
                        updatedAt = Clock.System.now().toEpochMilliseconds()
                    )
                )
                
                planCache[planCanvas.canvasId] = updatedPlan
            }
            
            else -> {
                // CREATED case - 新Template不需要更新现有Plan
            }
        }
    }
    
    /**
     * 更新Plan中的Draft引用
     */
    private suspend fun updatePlanDraftReference(
        planCanvas: PlanCanvasData,
        draft: TemplateDraft,
        changeType: ChangeType
    ) {
        when (changeType) {
            ChangeType.UPDATED -> {
                // 更新Plan中的Draft信息
                val updatedSchedule = updateDraftItemsInSchedule(
                    planCanvas.scheduleItems,
                    draft
                )
                
                val updatedPlan = planCanvas.copy(
                    scheduleItems = updatedSchedule,
                    metadata = planCanvas.metadata.copy(
                        updatedAt = Clock.System.now().toEpochMilliseconds()
                    )
                )
                
                planCache[planCanvas.canvasId] = updatedPlan
            }
            
            ChangeType.DELETED -> {
                // Draft被删除，从Plan中移除相关引用
                val updatedSchedule = removeDraftItemsFromSchedule(
                    planCanvas.scheduleItems,
                    draft.id
                )
                
                val updatedPlan = planCanvas.copy(
                    scheduleItems = updatedSchedule,
                    metadata = planCanvas.metadata.copy(
                        updatedAt = Clock.System.now().toEpochMilliseconds()
                    )
                )
                
                planCache[planCanvas.canvasId] = updatedPlan
            }
            
            else -> {
                // CREATED/PROMOTED case - 根据具体情况处理
            }
        }
    }
    
    /**
     * 更新Schedule中的Template items
     */
    private fun updateTemplateItemsInSchedule(
        scheduleItems: Map<Int, List<CanvasItem>>,
        template: WorkoutTemplate
    ): Map<Int, List<CanvasItem>> {
        return scheduleItems.mapValues { (_, items) ->
            items.map { item ->
                if (item is CanvasItem.TemplateItem && item.templateId == template.id) {
                    // 更新Template item的信息
                    item.copy(
                        name = template.name.toString(),
                        exerciseCount = template.exercises.size,
                        estimatedDuration = template.estimatedDuration,
                        targetMuscleGroups = template.targetMuscleGroups ?: emptyList(),
                        metadata = item.metadata + mapOf(
                            "lastUpdated" to Clock.System.now().toEpochMilliseconds().toString()
                        )
                    )
                } else {
                    item
                }
            }
        }
    }
    
    /**
     * 更新Schedule中的Draft items
     */
    private fun updateDraftItemsInSchedule(
        scheduleItems: Map<Int, List<CanvasItem>>,
        draft: TemplateDraft
    ): Map<Int, List<CanvasItem>> {
        return scheduleItems.mapValues { (_, items) ->
            items.map { item ->
                if (item is CanvasItem.DraftItem && item.draftId == draft.id) {
                    // 更新Draft item的信息
                    item.copy(
                        name = draft.name,
                        exerciseCount = draft.exercises.size,
                        estimatedDuration = draft.estimatedDuration,
                        targetMuscleGroups = draft.targetMuscleGroups,
                        canPromote = draft.canPromoteToTemplate(),
                        metadata = item.metadata + mapOf(
                            "version" to draft.version.toString(),
                            "lastUpdated" to Clock.System.now().toEpochMilliseconds().toString()
                        )
                    )
                } else {
                    item
                }
            }
        }
    }
    
    /**
     * 从Schedule中移除Template items
     */
    private fun removeTemplateItemsFromSchedule(
        scheduleItems: Map<Int, List<CanvasItem>>,
        templateId: String
    ): Map<Int, List<CanvasItem>> {
        return scheduleItems.mapValues { (_, items) ->
            items.filter { item ->
                !(item is CanvasItem.TemplateItem && item.templateId == templateId)
            }
        }.filterValues { it.isNotEmpty() }
    }
    
    /**
     * 从Schedule中移除Draft items
     */
    private fun removeDraftItemsFromSchedule(
        scheduleItems: Map<Int, List<CanvasItem>>,
        draftId: String
    ): Map<Int, List<CanvasItem>> {
        return scheduleItems.mapValues { (_, items) ->
            items.filter { item ->
                !(item is CanvasItem.DraftItem && item.draftId == draftId)
            }
        }.filterValues { it.isNotEmpty() }
    }
    
    /**
     * 清理Plan中的Draft引用（当Draft被删除时）
     */
    private suspend fun cleanupDraftReferencesInPlans(draftId: String) {
        val affectedPlans = findPlansReferencingDraft(draftId)
        
        affectedPlans.forEach { planCanvas ->
            val updatedSchedule = removeDraftItemsFromSchedule(
                planCanvas.scheduleItems,
                draftId
            )
            
            val updatedPlan = planCanvas.copy(
                scheduleItems = updatedSchedule,
                metadata = planCanvas.metadata.copy(
                    updatedAt = Clock.System.now().toEpochMilliseconds()
                )
            )
            
            planCache[planCanvas.canvasId] = updatedPlan
            
            // 通知Plan模块更新
            emitSyncEvent(
                SyncEvent.PlanUpdated(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "sync_manager",
                    planCanvas = updatedPlan,
                    changeType = ChangeType.UPDATED
                )
            )
        }
    }
    
    /**
     * 将Draft引用升级为Template引用（当Draft转正时）
     */
    private suspend fun promoteDraftReferencesToTemplate(draft: TemplateDraft) {
        // 假设草稿转正后生成的Template ID有某种规律或通过参数传入
        // 这里需要根据实际的业务逻辑调整
        val newTemplateId = "template_from_draft_${draft.id}"
        
        val affectedPlans = findPlansReferencingDraft(draft.id)
        
        affectedPlans.forEach { planCanvas ->
            val updatedSchedule = promoteDraftItemsToTemplateItems(
                planCanvas.scheduleItems,
                draft.id,
                newTemplateId,
                draft
            )
            
            val updatedPlan = planCanvas.copy(
                scheduleItems = updatedSchedule,
                metadata = planCanvas.metadata.copy(
                    updatedAt = Clock.System.now().toEpochMilliseconds()
                )
            )
            
            planCache[planCanvas.canvasId] = updatedPlan
            
            // 通知Plan模块更新
            emitSyncEvent(
                SyncEvent.PlanUpdated(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "sync_manager",
                    planCanvas = updatedPlan,
                    changeType = ChangeType.UPDATED
                )
            )
        }
    }
    
    /**
     * 将Draft items转换为Template items
     */
    private fun promoteDraftItemsToTemplateItems(
        scheduleItems: Map<Int, List<CanvasItem>>,
        draftId: String,
        newTemplateId: String,
        draft: TemplateDraft
    ): Map<Int, List<CanvasItem>> {
        return scheduleItems.mapValues { (_, items) ->
            items.map { item ->
                if (item is CanvasItem.DraftItem && item.draftId == draftId) {
                    // 将Draft item转换为Template item
                    CanvasItem.TemplateItem(
                        id = "template_${newTemplateId}_${item.position.getDayIndex()}",
                        name = draft.name,
                        position = item.position,
                        metadata = mapOf(
                            "source" to "promoted_draft",
                            "originalDraftId" to draftId,
                            "promotedAt" to Clock.System.now().toEpochMilliseconds().toString()
                        ),
                        templateId = newTemplateId,
                        estimatedDuration = draft.estimatedDuration,
                        exerciseCount = draft.exercises.size,
                        targetMuscleGroups = draft.targetMuscleGroups
                    )
                } else {
                    item
                }
            }
        }
    }
    
    /**
     * 验证Plan引用完整性
     */
    private suspend fun validatePlanReferences(planCanvas: PlanCanvasData) {
        val brokenReferences = mutableListOf<String>()
        
        planCanvas.scheduleItems.values.flatten().forEach { item ->
            when (item) {
                is CanvasItem.TemplateItem -> {
                    if (!templateCache.containsKey(item.templateId)) {
                        brokenReferences.add("Template:${item.templateId}")
                    }
                }
                is CanvasItem.DraftItem -> {
                    if (!draftCache.containsKey(item.draftId)) {
                        brokenReferences.add("Draft:${item.draftId}")
                    }
                }
                is CanvasItem.CustomItem -> {
                    // Custom items不需要外部引用验证
                }
            }
        }
        
        if (brokenReferences.isNotEmpty()) {
            emitSyncEvent(
                SyncEvent.SyncConflict(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "sync_manager",
                    conflictType = ConflictType.REFERENCE_BROKEN,
                    details = mapOf(
                        "planId" to planCanvas.canvasId,
                        "brokenReferences" to brokenReferences
                    )
                )
            )
        }
    }
    
    // === 辅助方法 ===
    
    private suspend fun emitSyncEvent(event: SyncEvent) {
        _syncEvents.emit(event)
        syncListeners.forEach { it.onSyncEvent(event) }
    }
    
    private fun updateSyncState(update: (SyncState) -> SyncState) {
        val newState = update(_syncState.value)
        _syncState.value = newState
        syncListeners.forEach { it.onSyncStateChanged(newState) }
    }
    
    private fun handleSyncError(error: Exception, module: String, itemId: String) {
        logger.e("CrossModuleSync", "同步错误 - 模块: $module, 项目: $itemId", error)
        
        updateSyncState { state ->
            state.copy(
                failedSyncCount = state.failedSyncCount + 1,
                isSyncing = false
            )
        }
    }
    
    /**
     * 批量同步（用于初始化或大量数据变更）
     */
    suspend fun batchSync(
        templates: List<WorkoutTemplate>,
        drafts: List<TemplateDraft>,
        plans: List<PlanCanvasData>,
        scope: CoroutineScope
    ) {
        logger.d("CrossModuleSync", "开始批量同步 - T:${templates.size}, D:${drafts.size}, P:${plans.size}")
        
        try {
            updateSyncState { it.copy(isSyncing = true) }
            
            // 更新缓存
            templates.forEach { templateCache[it.id] = it }
            drafts.forEach { draftCache[it.id] = it }
            plans.forEach { planCache[it.canvasId] = it }
            
            // 验证所有Plan的引用完整性
            plans.forEach { plan ->
                scope.launch {
                    validatePlanReferences(plan)
                }
            }
            
            emitSyncEvent(
                SyncEvent.SyncCompleted(
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    sourceModule = "sync_manager",
                    syncedItemsCount = templates.size + drafts.size + plans.size,
                    duration = 0 // TODO: 计算实际耗时
                )
            )
            
        } catch (e: Exception) {
            logger.e("CrossModuleSync", "批量同步失败", e)
            handleSyncError(e, "batch_sync", "all")
        } finally {
            updateSyncState { it.copy(isSyncing = false) }
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        templateCache.clear()
        draftCache.clear()
        planCache.clear()
        logger.d("CrossModuleSync", "缓存已清理")
    }
}